# Document Search Functionality

## Overview
This document describes the search functionality implemented for the SearchDocController.php file and the searchDoc.blade.php view.

## Features Implemented

### 1. Controller Function (SearchDocController@search)
- **Location**: `app/Http/Controllers/SearchDocController.php`
- **Method**: `search(SearchDocumentRequest $request)`
- **Functionality**:
  - Accepts search parameters from a validated form request
  - Queries the Document model/table with joins to category table
  - Derives file type from filename extension (since filetype field doesn't exist in current schema)
  - Sorts results by file type first, then by title
  - Returns filtered and sorted data as JSON response

### 2. Form Request Validation
- **Location**: `app/Http/Requests/SearchDocumentRequest.php`
- **Validates**:
  - `search_term`: Optional string for general search
  - `category_id`: Optional integer that must exist in m_category table

### 3. View Implementation
- **Location**: `resources/views/searchDoc.blade.php`
- **Features**:
  - Search form with multiple filter options
  - AJAX-powered search (no page reload required)
  - Results grouped by file type
  - Responsive card-based layout for results
  - File type icons and status badges
  - Smooth animations and hover effects

### 4. Search Criteria
The search functionality supports:
- **General Search**: Searches in title, description, filename, and category name
- **Category Filter**: Filter by specific document category
- **Automatic Status Filter**: Only shows documents with status = 'Approved'

### 5. Results Display
- Documents are grouped by file type for better organization
- Each result shows:
  - Document title and status
  - Category information
  - Description preview (truncated)
  - Filename
  - File type icon
  - "View Detail" button (placeholder for future implementation)

## Installation Steps

### 1. Database Setup
Run the seeder to add the search route:
```bash
php artisan db:seed --class=SearchDocSeeder
```

### 2. Route Configuration
The seeder adds this route:
- `POST /pencarian-dokumen/search` → `SearchDocController@search`

### 3. Dependencies
The implementation uses existing dependencies:
- Laravel Form Requests for validation
- jQuery for AJAX functionality
- Bootstrap/Metronic for styling
- SweetAlert2 for notifications

## Important Notes

### File Type Handling
Since the `t_document` table doesn't have a `filetype` field, the implementation:
- Derives file type from the `filename` field using SQL `SUBSTRING_INDEX` function
- Returns "UNKNOWN" for documents without filenames
- Supports filtering by file extension

### Future Enhancements
Consider adding:
1. A `filetype` column to the `t_document` table for better performance
2. Full-text search capabilities
3. Advanced filtering options (date ranges, file size, etc.)
4. Document preview functionality
5. Export search results feature

### Browser Compatibility
The implementation uses modern JavaScript (ES6 template literals) and requires:
- Modern browsers with ES6 support
- jQuery (already included in the main layout)

## Usage Example

### Basic Search
1. Enter keywords in the "Kata Kunci Pencarian" field
2. Click "Cari Dokumen" button
3. Results appear below the search form, grouped by file type

### Advanced Search
1. Use combination of filters:
   - Keywords + Category
   - Keywords only
   - Category only
2. Click "Reset" to clear all filters

### AJAX Response Format
```json
{
  "success": true,
  "message": "Search completed successfully",
  "data": {
    "documents": [...],
    "grouped_documents": {...},
    "total_count": 10
  }
}
```

## Error Handling
- Form validation errors are displayed using Laravel's validation system
- AJAX errors show user-friendly messages via Toastr notifications
- Database errors are caught and return appropriate error responses
