<?php

use Illuminate\Database\Seeder;

class SearchDocSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Instuksi Untuk Running Upload Document
        // 1. Create <PERSON><PERSON> di <PERSON>
        // 2. Create permision pencarian-dokumen
        // 3. php artisan migrate --path=/database/migrations/2025_09_03_155929_create_t_document_table.php
        // 4. php artisan db:seed --class=SearchDocSeeder

        $data = [
            ['permission' => 'pencarian-dokumen-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/pencarian-dokumen', 'route' => 'SearchDocController@index', 'guard' => 'web'],
            ['permission' => 'pencarian-dokumen-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/pencarian-dokumen/list', 'route' => 'SearchDocController@datatables', 'guard' => 'web'],
        ];

        foreach($data as $k_data => $v_data){
            $check = Routes::where('url', $v_data['url'])->where('method',$v_data['method'])->first();
            if(!$check){
                 Routes::create($v_data);
            }else{
                Routes::where('id',$check->id)->update($v_data);
            }
        }
        $this->command->info("Routes Seeder Success !");
    }
}
