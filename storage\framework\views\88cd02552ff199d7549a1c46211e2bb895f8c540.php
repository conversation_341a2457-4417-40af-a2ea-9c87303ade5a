<?php $__env->startSection('title', 'Pencarian Dokumen'); ?>

<?php $__env->startSection('css_page'); ?>

<style>
  /* width */
  ::-webkit-scrollbar {
    width: 5px;
  }

  /* Track */
  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: #c8c8c8;
  }

  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  /* color danger */
  .color-danger {
    color: #ff0000;
  }

  .text-danger {
    color: #F64E60 !important;
  }

  /* Search results styling */
  .card-stretch {
    height: 100%;
  }

  .search-result-card {
    transition: transform 0.2s ease-in-out;
  }

  .search-result-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  }

  .file-type-section {
    border-left: 4px solid #3699FF;
    padding-left: 15px;
    margin-bottom: 20px;
  }

  .results-fade-in {
    animation: fadeIn 0.5s ease-in;
  }

  @keyframes  fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="content classMasterData d-flex flex-column flex-column-fluid" style="padding-top: 10px !important; padding-bottom: 0px !important;" id="kt_content">
    <!--begin::Container-->
    <div class="container-fluid">
        <!--begin::Card-->
        <div class="card card-custom">
            <!--begin::Header-->
            <div class="card-header flex-wrap py-3">
                <div class="card-title">
                    <h3 class="card-label">Pencarian Dokumen
                        <span class="d-block text-muted pt-2 font-size-sm">Cari dokumen berdasarkan kriteria tertentu</span>
                    </h3>
                </div>
            </div>
            <!--end::Header-->

            <!--begin::Form-->
            <form id="searchDocumentForm" class="form">
                <?php echo csrf_field(); ?>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="search_term">Kata Kunci Pencarian:</label>
                                <input type="text" class="form-control" id="search_term" name="search_term"
                                       placeholder="Masukkan judul, deskripsi, atau nama file...">
                                <span class="form-text text-muted">Pencarian akan dilakukan pada judul, deskripsi, nama file, dan kategori</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="category_id">Kategori:</label>
                                <select class="form-control" id="category_id" name="category_id">
                                    <option value="">-- Semua Kategori --</option>
                                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($category['id']); ?>"><?php echo $category['name']; ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="status">Status:</label>
                                <select class="form-control" id="status" name="status">
                                    <option value="">-- Semua Status --</option>
                                    <option value="Waiting">Waiting</option>
                                    <option value="Approved">Approved</option>
                                    <option value="Rejected">Rejected</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="filetype">Tipe File:</label>
                                <input type="text" class="form-control" id="filetype" name="filetype"
                                       placeholder="pdf, jpg, mp4, dll...">
                                <span class="form-text text-muted">Masukkan ekstensi file (tanpa titik)</span>
                            </div>
                        </div>
                        <div class="col-md-9">
                            <div class="form-group d-flex align-items-end">
                                <button type="submit" id="searchBtn" class="btn btn-primary mr-3">
                                    <i class="fa fa-search"></i> Cari Dokumen
                                </button>
                                <button type="button" id="resetBtn" class="btn btn-light-primary">
                                    <i class="fa fa-times"></i> Reset
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
            <!--end::Form-->
        </div>
        <!--end::Card-->

        <!--begin::Results Card-->
        <div class="card card-custom mt-5" id="resultsCard" style="display: none;">
            <!--begin::Header-->
            <div class="card-header flex-wrap py-3">
                <div class="card-title">
                    <h3 class="card-label">Hasil Pencarian
                        <span class="d-block text-muted pt-2 font-size-sm" id="resultsCount">0 dokumen ditemukan</span>
                    </h3>
                </div>
            </div>
            <!--end::Header-->

            <!--begin::Body-->
            <div class="card-body" id="resultsContainer">
                <!-- Results will be populated here -->
            </div>
            <!--end::Body-->
        </div>
        <!--end::Results Card-->
    </div>
    <!--end::Container-->
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js_page'); ?>
<script>
$(document).ready(function() {
    // Handle search form submission
    $('#searchDocumentForm').on('submit', function(e) {
        e.preventDefault();
        performSearch();
    });

    // Handle reset button
    $('#resetBtn').on('click', function() {
        $('#searchDocumentForm')[0].reset();
        $('#resultsCard').hide();
    });

    function performSearch() {
        var formData = $('#searchDocumentForm').serialize();

        $.ajax({
            url: './pencarian-dokumen/search',
            type: 'POST',
            data: formData,
            dataType: 'json',
            beforeSend: function() {
                $('#searchBtn').attr('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Mencari...');
            },
            success: function(response) {
                if (response.success) {
                    displayResults(response.data);
                } else {
                    showtoastr('error', response.message || 'Terjadi kesalahan saat mencari dokumen');
                }
            },
            error: function(xhr, status, error) {
                var errorMessage = 'Terjadi kesalahan saat mencari dokumen';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                showtoastr('error', errorMessage);
            },
            complete: function() {
                $('#searchBtn').attr('disabled', false).html('<i class="fa fa-search"></i> Cari Dokumen');
            }
        });
    }

    function displayResults(data) {
        var documents = data.documents;
        var groupedDocuments = data.grouped_documents;
        var totalCount = data.total_count;

        // Update results count
        $('#resultsCount').text(totalCount + ' dokumen ditemukan');

        // Clear previous results
        $('#resultsContainer').empty();

        if (totalCount === 0) {
            $('#resultsContainer').html(`
                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i> Tidak ada dokumen yang sesuai dengan kriteria pencarian.
                </div>
            `);
        } else {
            // Display results grouped by filetype
            var resultsHtml = '';

            $.each(groupedDocuments, function(filetype, docs) {
                resultsHtml += `
                    <div class="file-type-section results-fade-in">
                        <h5 class="text-primary mb-4">
                            <i class="fa fa-file"></i> Tipe File: ${filetype} (${docs.length} dokumen)
                        </h5>
                        <div class="row">
                `;

                $.each(docs, function(index, doc) {
                    var statusBadge = getStatusBadge(doc.status);
                    var fileIcon = getFileIcon(doc.filetype);

                    resultsHtml += `
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card card-custom card-stretch search-result-card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="symbol symbol-40 symbol-light-primary mr-3">
                                            <span class="symbol-label">
                                                <i class="${fileIcon} text-primary"></i>
                                            </span>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="text-dark-75 text-hover-primary mb-1 font-weight-bold">
                                                ${doc.title}
                                            </h6>
                                            ${statusBadge}
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <span class="text-muted font-size-sm">Kategori:</span>
                                        <span class="text-dark-75 font-weight-bold">${doc.category_name || 'Tidak ada kategori'}</span>
                                    </div>

                                    ${doc.description ? `
                                        <div class="mb-3">
                                            <span class="text-muted font-size-sm">Deskripsi:</span>
                                            <p class="text-dark-75 font-size-sm mb-0">${doc.description.substring(0, 100)}${doc.description.length > 100 ? '...' : ''}</p>
                                        </div>
                                    ` : ''}

                                    <div class="mb-3">
                                        <span class="text-muted font-size-sm">File:</span>
                                        <span class="text-dark-75 font-weight-bold">${doc.filename || 'Tidak ada file'}</span>
                                    </div>

                                    <div class="text-right">
                                        <button class="btn btn-sm btn-light-primary" onclick="viewDocument(${doc.id})">
                                            <i class="fa fa-eye"></i> Lihat Detail
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });

                resultsHtml += `
                        </div>
                    </div>
                `;
            });

            $('#resultsContainer').html(resultsHtml);
        }

        // Show results card
        $('#resultsCard').show();

        // Scroll to results
        $('html, body').animate({
            scrollTop: $('#resultsCard').offset().top - 100
        }, 500);
    }

    function getStatusBadge(status) {
        var badgeClass = '';
        switch(status) {
            case 'Approved':
                badgeClass = 'badge-success';
                break;
            case 'Rejected':
                badgeClass = 'badge-danger';
                break;
            case 'Waiting':
            default:
                badgeClass = 'badge-warning';
                break;
        }
        return `<span class="badge ${badgeClass}">${status}</span>`;
    }

    function getFileIcon(filetype) {
        switch(filetype.toLowerCase()) {
            case 'pdf':
                return 'fa fa-file-pdf';
            case 'jpg':
            case 'jpeg':
            case 'png':
            case 'gif':
                return 'fa fa-file-image';
            case 'mp4':
            case 'avi':
            case 'mov':
                return 'fa fa-file-video';
            case 'doc':
            case 'docx':
                return 'fa fa-file-word';
            case 'xls':
            case 'xlsx':
                return 'fa fa-file-excel';
            case 'ppt':
            case 'pptx':
                return 'fa fa-file-powerpoint';
            default:
                return 'fa fa-file';
        }
    }

    // Function to view document details (placeholder)
    window.viewDocument = function(documentId) {
        // This can be implemented to show document details in a modal or redirect to detail page
        showtoastr('info', 'Fitur detail dokumen akan segera tersedia');
    };
});
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.main', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\dev-dmm\resources\views/searchDoc.blade.php ENDPATH**/ ?>