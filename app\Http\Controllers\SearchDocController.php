<?php

namespace App\Http\Controllers;

use App\Models\Document;
use App\Models\Menu;
use App\Models\Category;
use App\Http\Requests\SearchDocumentRequest;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;
use Exception;
use function response;
use function responseFail;
use function responseSuccess;
use function trans;
use function view;

class SearchDocController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
      $data = [
          'title' => 'Pencarian Dokumen',
          'breadcrumb' => [
              [
                  'title' => 'Panduan Pemeliharaan',
                  'url' => '/pencarian-dokumen',
              ],
              [
                  'title' => 'Pencarian Dokumen',
                  'url' => '',
              ]
          ],
      ];
      $data['menus'] = $this->getDashboardMenu();
      $data['menu'] = Menu::select('id', 'name')->get();
      $data['categories'] = Category::getTree();

      return view('searchDoc', $data);
    }

    public function datatables(Request $request)
    {
        $query = Document::select('t_document.*', 'm_category.name as category_name')
                ->leftjoin('m_category', 'm_category.id', 't_document.category_id')
                ->orderBy('id')->get();
        $data = DataTables::of($query)->make(true);
        $response = $data->getData(true);

        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    /**
     * Search documents based on provided criteria
     *
     * @param SearchDocumentRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function search(SearchDocumentRequest $request)
    {
        try {
            $query = Document::select(
                't_document.*',
                'm_category.name as category_name',
                DB::raw('CASE
                    WHEN t_document.filename IS NOT NULL
                    THEN UPPER(SUBSTRING_INDEX(t_document.filename, ".", -1))
                    ELSE "UNKNOWN"
                END as filetype')
            )
            ->leftJoin('m_category', 'm_category.id', '=', 't_document.category_id')
            ->where('t_document.status', 'Approved'); // Only show approved documents

            // Apply search filters
            if ($request->filled('search_term')) {
                $searchTerm = $request->search_term;
                $query->where(function($q) use ($searchTerm) {
                    $q->where('t_document.title', 'LIKE', "%{$searchTerm}%")
                      ->orWhere('t_document.description', 'LIKE', "%{$searchTerm}%")
                      ->orWhere('t_document.filename', 'LIKE', "%{$searchTerm}%")
                      ->orWhere('m_category.name', 'LIKE', "%{$searchTerm}%");
                });
            }

            if ($request->filled('category_id')) {
                $query->where('t_document.category_id', $request->category_id);
            }

            // Sort by filetype first, then by title
            $documents = $query->orderByRaw('filetype ASC, t_document.title ASC')->get();

            // Group documents by filetype for better organization
            $groupedDocuments = $documents->groupBy('filetype');

            $response = responseSuccess('Search completed successfully', [
                'documents' => $documents,
                'grouped_documents' => $groupedDocuments,
                'total_count' => $documents->count()
            ]);

            return response()->json($response, 200);

        } catch (Exception $e) {
            $response = responseFail('Search failed', [$e->getMessage()]);
            return response()->json($response, 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

}
