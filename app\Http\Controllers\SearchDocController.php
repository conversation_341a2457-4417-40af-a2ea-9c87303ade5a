<?php

namespace App\Http\Controllers;

use App\Models\Document;
use App\Models\Menu;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;
use Exception;
use function response;
use function responseFail;
use function responseSuccess;
use function trans;
use function view;

class SearchDocController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
      $data = [
          'title' => 'Pencarian Dokumen',
          'breadcrumb' => [
              [
                  'title' => 'Panduan Pemeliharaan',
                  'url' => '/pencarian-dokumen',
              ],
              [
                  'title' => 'Pencarian Dokumen',
                  'url' => '',
              ]
          ],
      ];
      $data['menus'] = $this->getDashboardMenu();
      $data['menu'] = Menu::select('id', 'name')->get();

      return view('searchDoc', $data);
    }

    public function datatables(Request $request)
    {
        $query = Document::select('t_document.*', 'm_category.name as category_name')
                ->leftjoin('m_category', 'm_category.id', 't_document.category_id')
                ->orderBy('id')->get();
        $data = DataTables::of($query)->make(true);
        $response = $data->getData(true);

        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

}
