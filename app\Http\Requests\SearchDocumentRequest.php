<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SearchDocumentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'search_term' => 'nullable|string|max:255',
            'category_id' => 'nullable|integer|exists:m_category,id',
            'status' => 'nullable|string|in:Waiting,Approved,Rejected',
            'filetype' => 'nullable|string|max:10',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'category_id.exists' => 'The selected category is invalid.',
            'status.in' => 'The status must be one of: Waiting, Approved, Rejected.',
            'search_term.max' => 'The search term may not be greater than 255 characters.',
            'filetype.max' => 'The file type may not be greater than 10 characters.',
        ];
    }
}
